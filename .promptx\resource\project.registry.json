{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-06-24T22:55:55.212Z", "updatedAt": "2025-06-24T22:55:55.215Z", "resourceCount": 15}, "resources": [{"id": "java-ai-interviewer", "source": "project", "protocol": "role", "name": "Java Ai Interviewer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/java-ai-interviewer.role.md", "metadata": {"createdAt": "2025-06-24T22:55:55.213Z", "updatedAt": "2025-06-24T22:55:55.213Z", "scannedAt": "2025-06-24T22:55:55.213Z"}}, {"id": "behavioral-assessment", "source": "project", "protocol": "thought", "name": "Behavioral Assessment 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/thought/behavioral-assessment.thought.md", "metadata": {"createdAt": "2025-06-24T22:55:55.213Z", "updatedAt": "2025-06-24T22:55:55.213Z", "scannedAt": "2025-06-24T22:55:55.213Z"}}, {"id": "multi-perspective-evaluation", "source": "project", "protocol": "thought", "name": "Multi Perspective Evaluation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/thought/multi-perspective-evaluation.thought.md", "metadata": {"createdAt": "2025-06-24T22:55:55.213Z", "updatedAt": "2025-06-24T22:55:55.213Z", "scannedAt": "2025-06-24T22:55:55.213Z"}}, {"id": "skeptical-validation", "source": "project", "protocol": "thought", "name": "Skeptical Validation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/thought/skeptical-validation.thought.md", "metadata": {"createdAt": "2025-06-24T22:55:55.213Z", "updatedAt": "2025-06-24T22:55:55.213Z", "scannedAt": "2025-06-24T22:55:55.213Z"}}, {"id": "technical-depth-assessment", "source": "project", "protocol": "thought", "name": "Technical Depth Assessment 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/thought/technical-depth-assessment.thought.md", "metadata": {"createdAt": "2025-06-24T22:55:55.213Z", "updatedAt": "2025-06-24T22:55:55.213Z", "scannedAt": "2025-06-24T22:55:55.213Z"}}, {"id": "interview-process", "source": "project", "protocol": "execution", "name": "Interview Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/execution/interview-process.execution.md", "metadata": {"createdAt": "2025-06-24T22:55:55.213Z", "updatedAt": "2025-06-24T22:55:55.213Z", "scannedAt": "2025-06-24T22:55:55.213Z"}}, {"id": "question-generation", "source": "project", "protocol": "execution", "name": "Question Generation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/execution/question-generation.execution.md", "metadata": {"createdAt": "2025-06-24T22:55:55.213Z", "updatedAt": "2025-06-24T22:55:55.213Z", "scannedAt": "2025-06-24T22:55:55.213Z"}}, {"id": "resume-analysis", "source": "project", "protocol": "execution", "name": "Resume Analysis 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/execution/resume-analysis.execution.md", "metadata": {"createdAt": "2025-06-24T22:55:55.213Z", "updatedAt": "2025-06-24T22:55:55.213Z", "scannedAt": "2025-06-24T22:55:55.213Z"}}, {"id": "hr-recruitment-standards", "source": "project", "protocol": "knowledge", "name": "Hr Recruitment Standards 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/knowledge/hr-recruitment-standards.knowledge.md", "metadata": {"createdAt": "2025-06-24T22:55:55.213Z", "updatedAt": "2025-06-24T22:55:55.213Z", "scannedAt": "2025-06-24T22:55:55.213Z"}}, {"id": "interview-methodology", "source": "project", "protocol": "knowledge", "name": "Interview Methodology 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/knowledge/interview-methodology.knowledge.md", "metadata": {"createdAt": "2025-06-24T22:55:55.214Z", "updatedAt": "2025-06-24T22:55:55.214Z", "scannedAt": "2025-06-24T22:55:55.214Z"}}, {"id": "java-ai-technical-stack", "source": "project", "protocol": "knowledge", "name": "Java Ai Technical Stack 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/java-ai-interviewer/knowledge/java-ai-technical-stack.knowledge.md", "metadata": {"createdAt": "2025-06-24T22:55:55.214Z", "updatedAt": "2025-06-24T22:55:55.214Z", "scannedAt": "2025-06-24T22:55:55.214Z"}}, {"id": "resume-master", "source": "project", "protocol": "role", "name": "Resume Master 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/domain/resume-master/resume-master.role.md", "metadata": {"createdAt": "2025-06-24T22:55:55.214Z", "updatedAt": "2025-06-24T22:55:55.214Z", "scannedAt": "2025-06-24T22:55:55.214Z"}}, {"id": "resume-psychology", "source": "project", "protocol": "thought", "name": "Resume Psychology 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/domain/resume-master/thought/resume-psychology.thought.md", "metadata": {"createdAt": "2025-06-24T22:55:55.214Z", "updatedAt": "2025-06-24T22:55:55.214Z", "scannedAt": "2025-06-24T22:55:55.214Z"}}, {"id": "resume-crafting", "source": "project", "protocol": "execution", "name": "Resume Crafting 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/domain/resume-master/execution/resume-crafting.execution.md", "metadata": {"createdAt": "2025-06-24T22:55:55.214Z", "updatedAt": "2025-06-24T22:55:55.214Z", "scannedAt": "2025-06-24T22:55:55.214Z"}}, {"id": "resume-expertise", "source": "project", "protocol": "knowledge", "name": "Resume Expertise 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/domain/resume-master/knowledge/resume-expertise.knowledge.md", "metadata": {"createdAt": "2025-06-24T22:55:55.214Z", "updatedAt": "2025-06-24T22:55:55.214Z", "scannedAt": "2025-06-24T22:55:55.214Z"}}], "stats": {"totalResources": 15, "byProtocol": {"role": 2, "thought": 5, "execution": 4, "knowledge": 4}, "bySource": {"project": 15}}}